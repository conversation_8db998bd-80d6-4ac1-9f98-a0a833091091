<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KafkaTool - Postman Style</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        },
                        sidebar: {
                            bg: '#1e293b',
                            hover: '#334155',
                            active: '#3b82f6'
                        }
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">

    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/material.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/hint/show-hint.min.css">

    <!-- Custom CSS for CodeMirror integration -->
    <style>
        .CodeMirror {
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.4;
            height: auto;
            min-height: 200px;
        }
        .CodeMirror-focused {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }
        .CodeMirror-scroll {
            min-height: 200px;
            max-height: 400px;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans antialiased">
    <!-- Main Layout Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
    <div class="w-72 bg-sidebar-bg text-white flex flex-col border-r border-gray-700">
        <!-- Sidebar Header -->
        <div class="p-4 border-b border-gray-700">
            <h2 class="text-xl font-semibold text-white">KafkaTool</h2>
        </div>

        <!-- New Task Button -->
        <div class="p-4">
            <button id="newTaskBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                新建任务
            </button>
        </div>

        <!-- My Tasks Section -->
        <div class="flex-1 overflow-hidden flex flex-col">
            <div class="px-4 py-2">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">我的任务</h3>
            </div>
            <ul id="taskList" class="flex-1 overflow-y-auto px-2">
                <!-- 任务列表将通过JavaScript动态加载 -->
            </ul>
        </div>

        <!-- Management Section -->
        <div class="border-t border-gray-700">
            <div class="px-4 py-2">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">管理</h3>
            </div>
            <ul class="px-2 pb-4">
                <li id="env-management-btn" class="mb-1">
                    <button class="w-full text-left px-3 py-2 rounded-lg hover:bg-sidebar-hover transition-colors duration-200 flex items-center text-gray-300 hover:text-white">
                        <span class="mr-3">🌐</span>
                        <span>环境变量</span>
                    </button>
                </li>
                <li id="tpl-management-btn" class="mb-1">
                    <button class="w-full text-left px-3 py-2 rounded-lg hover:bg-sidebar-hover transition-colors duration-200 flex items-center text-gray-300 hover:text-white">
                        <span class="mr-3">📄</span>
                        <span>消息模板</span>
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col bg-white">
        <!-- Main Header -->
        <div class="bg-gray-50 border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <input type="text" id="taskNameInput" placeholder="任务名称"
                   class="flex-1 mr-4 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors">
            <button id="runBtn" class="bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                </svg>
                运行
            </button>
        </div>

        <!-- Tabs Container -->
        <div class="flex-1 flex flex-col">
            <!-- Tab Navigation -->
            <div class="bg-gray-50 border-b border-gray-200 px-6">
                <nav class="flex space-x-8">
                    <button class="tab-button py-4 px-1 border-b-2 border-blue-500 text-blue-600 font-medium text-sm" data-tab="config">
                        任务配置
                    </button>
                    <button class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" data-tab="run-output">
                        运行输出
                    </button>
                    <button class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" data-tab="run-history">
                        运行历史
                    </button>
                    <button class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" data-tab="charts">
                        统计图表
                    </button>
                </nav>
            </div>

            <!-- 任务配置Tab -->
            <div id="configTabContent" class="tab-content flex-1 overflow-y-auto">
                <div class="max-w-4xl mx-auto p-6 space-y-8">
                    <!-- 基本设置 -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">基本设置</h4>
                        <div class="space-y-4">
                            <div>
                                <label for="taskDescription" class="block text-sm font-medium text-gray-700 mb-2">任务描述</label>
                                <input type="text" id="taskDescription" placeholder="输入任务描述"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none">
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="cycleDuration" class="block text-sm font-medium text-gray-700 mb-2">周期时长 (秒)</label>
                                    <input type="number" id="cycleDuration" value="60"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none">
                                </div>
                                <div>
                                    <label for="totalDuration" class="block text-sm font-medium text-gray-700 mb-2">总运行时长 (秒, 0为无限)</label>
                                    <input type="number" id="totalDuration" value="300"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 队列配置 -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-semibold text-gray-900">队列配置</h4>
                            <button id="addQueueBtn" class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium px-4 py-2 rounded-md transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                添加队列
                            </button>
                        </div>
                        <div id="queuesContainer" class="space-y-4">
                            <!-- 队列项将通过JavaScript动态添加 -->
                        </div>
                    </div>

                    <!-- 环境配置 -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">环境配置</h4>
                        <div class="space-y-4">
                            <div>
                                <label for="environmentSelect" class="block text-sm font-medium text-gray-700 mb-2">选择环境</label>
                                <select id="environmentSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none">
                                    <option value="">选择环境...</option>
                                </select>
                            </div>
                            <div>
                                <label for="envVariablesPreview" class="block text-sm font-medium text-gray-700 mb-2">环境变量预览 (只读)</label>
                                <textarea id="envVariablesPreview" readonly rows="8"
                                          class="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md font-mono text-sm resize-none"
                                          placeholder="选择环境后将显示环境变量"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Save Task Button -->
                    <div class="flex justify-end">
                        <button id="saveTaskBtn" title="保存当前任务配置"
                                class="bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-2 rounded-lg transition-colors duration-200 flex items-center shadow-sm">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            保存任务
                        </button>
                    </div>
                </div>
            </div>

            <!-- 运行输出Tab -->
            <div id="runOutputTabContent" class="tab-content hidden flex-1 flex flex-col">
                <!-- Log Controls -->
                <div class="bg-gray-50 border-b border-gray-200 px-6 py-4 flex flex-wrap items-center justify-between gap-4">
                    <div class="flex items-center gap-4">
                        <div class="flex items-center gap-2">
                            <label for="maxLogLinesInput" class="text-sm font-medium text-gray-700">最大日志条数:</label>
                            <input type="number" id="maxLogLinesInput" value="10" min="5" max="1000"
                                   class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none">
                            <button id="applyLogSettingsBtn" class="bg-green-600 hover:bg-green-700 text-white text-xs font-medium px-3 py-1 rounded transition-colors duration-200 flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                应用
                            </button>
                        </div>
                        <button id="clearLogBtn" class="bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium px-4 py-2 rounded transition-colors duration-200">
                            清除日志
                        </button>
                        <div class="flex items-center gap-2">
                            <input type="checkbox" id="autoScrollLog" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="autoScrollLog" class="text-sm text-gray-700">自动滚动</label>
                        </div>
                    </div>
                    <input type="text" id="logFilterInput" placeholder="筛选日志..."
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none">
                </div>

                <!-- Status Bar -->
                <div class="bg-gray-100 border-b border-gray-200 px-6 py-3">
                    <div class="flex flex-wrap items-center gap-6 text-sm">
                        <span class="flex items-center gap-2">
                            <span class="text-gray-600">状态:</span>
                            <span id="runStatus" class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">就绪</span>
                        </span>
                        <span class="text-gray-600">消息数: <span id="runMessageCount" class="font-semibold text-gray-900">0</span></span>
                        <span class="text-gray-600">周期数: <span id="runCycleCount" class="font-semibold text-gray-900">0</span></span>
                        <span class="text-gray-600">错误数: <span id="runErrorCount" class="font-semibold text-red-600">0</span></span>
                        <span class="text-gray-600">已运行时长: <span id="runElapsedTime" class="font-semibold text-gray-900">00:00:00</span></span>
                    </div>
                </div>

                <!-- Console Output -->
                <div class="flex-1 p-6">
                    <div id="consoleOutput" class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm h-full overflow-y-auto min-h-96 whitespace-pre-wrap">
                        <div class="text-blue-400">欢迎使用 KafkaTool！</div>
                        <div class="text-blue-400">请配置任务参数并点击运行按钮开始测试。</div>
                    </div>
                </div>
            </div>

            <!-- 运行历史Tab -->
            <div id="runHistoryTabContent" class="tab-content hidden flex-1 flex flex-col">
                <div class="p-6">
                    <!-- History Controls -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                        <div class="flex flex-wrap items-center gap-4">
                            <button id="refreshHistoryBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新历史
                            </button>
                            <input type="text" id="historyFilterInput" placeholder="筛选历史记录..."
                                   class="flex-1 min-w-64 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none">
                            <button id="clearTaskHistoryBtn" class="bg-red-600 hover:bg-red-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                清空当前任务历史
                            </button>
                            <span id="historySummary" class="text-sm text-gray-600 font-medium">暂无历史记录</span>
                        </div>
                    </div>

                    <!-- History Items Container -->
                    <div id="historyItemsContainer" class="max-h-96 overflow-y-auto">
                        <div id="historyEmptyState" class="text-center py-16 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h5 class="text-lg font-medium text-gray-600 mb-2">暂无运行历史记录</h5>
                            <p class="text-gray-500">运行任务后，历史记录将显示在这里</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计图表Tab -->
            <div id="chartsTabContent" class="tab-content hidden flex-1 flex flex-col">
                <div class="p-6">
                    <!-- Charts Header -->
                    <div class="flex justify-between items-center mb-6">
                        <h4 class="text-xl font-semibold text-gray-900">统计图表</h4>
                        <div class="flex items-center gap-4">
                            <span id="chartTaskName" class="text-sm text-gray-600">未选择任务</span>
                            <button id="refreshChartsBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新图表
                            </button>
                        </div>
                    </div>

                    <div id="chartsContainer" class="bg-white border border-gray-200 rounded-lg p-6">
                        <!-- Charts Controls -->
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h5 class="text-lg font-semibold text-gray-900 mb-2">最后一次运行统计</h5>
                                    <div id="chartsSummary" class="text-sm text-gray-600">正在加载统计数据...</div>
                                </div>
                                <div class="flex gap-6">
                                    <div class="text-center">
                                        <div class="text-xs text-gray-500 mb-1">运行状态</div>
                                        <div id="chartRunStatus" class="font-semibold text-gray-900">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xs text-gray-500 mb-1">总消息数</div>
                                        <div id="chartTotalMessages" class="font-semibold text-gray-900">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xs text-gray-500 mb-1">运行时长</div>
                                        <div id="chartDuration" class="font-semibold text-gray-900">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Grid -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 消息发送趋势图 -->
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <h6 class="text-base font-semibold text-gray-900 mb-4">消息发送趋势</h6>
                                <div class="relative h-80">
                                    <canvas id="messagesTrendChart"></canvas>
                                </div>
                            </div>

                            <!-- 队列分布图 -->
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <h6 class="text-base font-semibold text-gray-900 mb-4">队列消息分布</h6>
                                <div class="relative h-80">
                                    <canvas id="queueDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div id="chartsEmptyState" class="hidden text-center py-16">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <h5 class="text-lg font-medium text-gray-600 mb-2">暂无统计数据</h5>
                            <p class="text-gray-500">运行任务后，统计图表将显示在这里</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Task Modal -->
    <div id="newTaskModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>新建任务</h3>
                <button class="modal-close-btn" data-modal-id="newTaskModal">&times;</button>
            </div>
            <div class="modal-body" style="padding: 20px; max-height: 60vh; overflow-y: auto;">
                    <!-- 基本设置 -->
                    <div class="form-section">
                        <h4>基本设置</h4>
                        <div class="form-group">
                            <label for="newTaskName">任务名称</label>
                            <input type="text" id="newTaskName" placeholder="输入任务名称">
                        </div>
                        <div class="form-group">
                            <label for="newTaskDescription">任务描述</label>
                            <input type="text" id="newTaskDescription" placeholder="输入任务描述">
                        </div>
                        <div class="form-group" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label for="newCycleDuration">周期时长 (秒)</label>
                                <input type="number" id="newCycleDuration" value="60">
                            </div>
                            <div>
                                <label for="newTotalDuration">总运行时长 (秒, 0为无限)</label>
                                <input type="number" id="newTotalDuration" value="300">
                            </div>
                        </div>
                    </div>

                    <!-- 队列配置 -->
                    <div class="form-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4>队列配置</h4>
                            <button id="newTaskAddQueueBtn" type="button" style="padding: 5px 10px; font-size: 0.9em;">＋ 添加队列</button>
                        </div>
                        <div id="newTaskQueuesContainer">
                            <!-- 队列项将通过JavaScript动态添加 -->
                            <div style="text-align: center; color: #95a5a6; padding: 20px; border: 1px dashed #ddd; border-radius: 4px;">
                                点击"添加队列"开始配置消息队列
                            </div>
                        </div>
                    </div>

                    <!-- 环境配置 -->
                    <div class="form-section">
                        <h4>环境配置</h4>
                        <div class="form-group">
                            <label for="newEnvironmentSelect">选择环境</label>
                            <select id="newEnvironmentSelect">
                                <option value="">选择环境...</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="newEnvVariablesPreview">环境变量预览 (只读)</label>
                            <textarea id="newEnvVariablesPreview" readonly rows="6" style="background-color: #f8f9fa; font-family: 'Courier New', monospace; font-size: 13px; box-sizing: border-box;" placeholder="选择环境后将显示环境变量"></textarea>
                        </div>
                    </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="newTaskModal">取消</button>
                <button class="btn-primary" id="createNewTaskBtn">创建任务</button>
            </div>
        </div>
    </div>

    <!-- Environment Management Modal -->
    <div id="envManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>环境变量管理</h4>
                <button class="modal-close-btn" data-modal-id="envManagementModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="modal-split-layout">
                    <!-- Left Panel - Environment List -->
                    <div class="modal-left-panel">
                        <div class="modal-left-header">
                            <h5>环境列表</h5>
                            <button class="btn-sm btn-primary" id="newEnvBtn">新建</button>
                        </div>
                        <div class="modal-left-content">
                            <div id="envList">
                                <!-- Environment list items will be dynamically loaded -->
                            </div>
                        </div>
                        <div class="modal-left-footer">
                            <button id="importEnvBtn" class="btn-sm btn-secondary" style="width: 100%;">导入环境...</button>
                        </div>
                    </div>

                    <!-- Right Panel - Environment Editor -->
                    <div class="modal-right-panel">
                        <div class="modal-right-header">
                            <h5>环境详情</h5>
                        </div>
                        <div class="modal-right-content">
                            <div class="form-group">
                                <label for="envName">环境名称</label>
                                <input type="text" id="envName" style="width: 100%;">
                            </div>
                            <div class="form-group">
                                <label for="envDesc">描述 (可选)</label>
                                <input type="text" id="envDesc" style="width: 100%;">
                            </div>
                            <div class="form-group">
                                <label for="envVars">环境变量 (K=V 格式, 每行一个)</label>
                                <textarea id="envVarsTextarea" rows="12" style="width: 100%; font-family: monospace; font-size: 0.9em;"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="envManagementModal">取消</button>
                <button class="btn-primary" id="saveEnvBtn">保存更改</button>
            </div>
        </div>
    </div>

    <!-- Message Templates Modal -->
    <div id="tplManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>消息模板管理</h4>
                <button class="modal-close-btn" data-modal-id="tplManagementModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="modal-split-layout">
                    <!-- Left Panel - Template List -->
                    <div class="modal-left-panel">
                        <div class="modal-left-header">
                            <h5>模板列表</h5>
                            <button class="btn-sm btn-primary" id="newTplBtn">新建</button>
                        </div>
                        <div class="modal-left-content">
                            <div id="tplList">
                                <!-- Template list items will be dynamically loaded -->
                            </div>
                        </div>
                        <div class="modal-left-footer">
                            <button id="importTplBtn" class="btn-sm btn-secondary" style="width: 100%;">导入模板...</button>
                        </div>
                    </div>

                    <!-- Right Panel - Template Editor -->
                    <div class="modal-right-panel">
                        <div class="modal-right-header">
                            <h5>模板详情</h5>
                        </div>
                        <div class="modal-right-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="tplName" style="font-weight: 500; font-size: 14px;">模板名称</label>
                                        <input type="text" id="tplName" style="width: 100%;">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="tplTopic" style="font-weight: 500; font-size: 14px;">默认Topic</label>
                                        <input type="text" id="tplTopic" style="width: 100%;" placeholder="例如: orders.new">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label for="tplContent" class="mb-0" style="font-weight: 500; font-size: 14px;">Python代码</label>
                                    <div style="display: flex; gap: 8px;">
                                        <button id="previewEnvBtn" class="btn btn-sm" style="background: #95a5a6; color: white; border: none; padding: 4px 8px; font-size: 12px;" title="配置预览环境变量">
                                            <i data-feather="settings" style="width: 14px; height: 14px;"></i> 预览变量
                                        </button>
                                        <button id="previewTplBtn" class="btn btn-sm" style="background: #3498db; color: white; border: none; padding: 4px 12px; font-size: 12px;">
                                            <i data-feather="eye" style="width: 14px; height: 14px; margin-right: 4px;"></i> 预览
                                        </button>
                                    </div>
                                </div>
                                <div class="code-editor-container">
                                    <div class="code-editor-toolbar">
                                        <div class="toolbar-left">
                                            <span>Python</span>
                                            <span style="color: #666;">|</span>
                                            <span style="font-size: 0.8em; color: #666;">Ctrl+Space 自动补全</span>
                                        </div>
                                        <div class="toolbar-right">
                                            <label for="themeSelector" style="font-size: 0.8em;">主题:</label>
                                            <select id="themeSelector" class="theme-selector">
                                                <option value="default">默认</option>
                                                <option value="material">Material</option>
                                                <option value="monokai">Monokai</option>
                                            </select>
                                        </div>
                                    </div>
                                    <textarea id="tplContentTextarea" style="display: none;" placeholder="def generate_message(env, device_id, device_ip, index, timestamp):
    return {
        'id': index,
        'timestamp': timestamp,
        'data': 'your message data'
    }"></textarea>
                                </div>
                            </div>
                            <div style="margin-top: 15px;">
                                <span id="testResult" style="font-size: 0.9em; color: #666;"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="tplManagementModal">取消</button>
                <button class="btn-primary" id="saveTplBtn">保存模板</button>
            </div>
        </div>
    </div>

    <!-- 预览环境变量编辑弹窗 -->
    <div id="previewEnvModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>预览环境变量</h3>
                <button class="modal-close-btn" data-modal-id="previewEnvModal">&times;</button>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <div class="form-group">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 8px;">
                        <label for="previewEnvVars" style="font-weight: 500; margin: 0;">环境变量 (JSON格式)</label>
                        <span id="currentTemplateName" style="font-size: 12px; color: #666; font-style: italic;"></span>
                    </div>
                    <textarea id="previewEnvVars" rows="15" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1;" placeholder='{
  "API_HOST": "localhost",
  "API_PORT": "8080",
  "DEBUG": "true"
}'></textarea>
                </div>
                <div style="font-size: 12px; color: #666; margin-top: 8px;">
                    💡 提示：这些环境变量仅用于预览，保存模板时会一起保存
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="previewEnvModal">取消</button>
                <button class="btn-primary" id="savePreviewEnvBtn">确定</button>
            </div>
        </div>
    </div>

    <!-- 预览结果弹窗 -->
    <div id="previewModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>模板预览结果</h3>
                <button class="modal-close-btn" data-modal-id="previewModal">&times;</button>
            </div>
            <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
                <div id="previewContent" style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.5; padding: 15px; background: #f8f9fa; border-radius: 5px; border: 1px solid #e9ecef;">
                    <!-- 预览内容将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" data-modal-id="previewModal">关闭</button>
            </div>
        </div>
    </div>

    <!-- CodeMirror JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/hint/show-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/hint/python-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/selection/active-line.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/fold/foldcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/fold/foldgutter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/fold/indent-fold.min.js"></script>

    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>

    <!-- Socket.IO Client -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket-manager.js"></script>
    <script src="js/components/task-manager.js"></script>
    <script src="js/components/queue-manager.js"></script>
    <script src="js/components/environment-manager.js"></script>
    <script src="js/components/template-manager.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 初始化Feather图标
        feather.replace();

        // 绑定刷新图表按钮事件
        document.addEventListener('DOMContentLoaded', () => {
            const refreshChartsBtn = document.getElementById('refreshChartsBtn');
            if (refreshChartsBtn) {
                refreshChartsBtn.addEventListener('click', () => {
                    if (window.app) {
                        window.app.loadStatisticsCharts();
                    }
                });
            }
        });
    </script>
</body>
</html>
