/* Custom styles to complement Tailwind CSS */

/* Modern scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.4);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.6);
}

::-webkit-scrollbar-corner {
    background: transparent;
}

/* Firefox scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
}

/* Dark theme scrollbar for console */
#consoleOutput::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.5);
}

#consoleOutput::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.7);
}

/* Sidebar scrollbar */
#taskList::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
}

#taskList::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.5);
}

/* Layout fixes */
html, body {
    height: 100%;
    overflow: hidden;
}

/* Tab functionality */
.tab-content {
    display: none;
    min-height: 0;
    flex: 1;
}

.tab-content.active {
    display: flex;
}

/* Tab button active state */
.tab-button.active {
    border-bottom-color: #3b82f6 !important;
    color: #3b82f6 !important;
}

/* Task list item styles */
.task-list li {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.25rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 2.75rem;
}

.task-list li:hover {
    background-color: #334155;
}

.task-list li.active {
    background-color: #3b82f6;
    color: white;
    border-left: 4px solid #60a5fa;
}

.task-name {
    flex: 1;
    margin-right: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.875rem;
}

.task-actions {
    display: flex;
    gap: 0.25rem;
    flex-shrink: 0;
}

.task-actions button {
    background: transparent;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    font-size: 0.875rem;
}

.task-actions button:hover {
    color: white;
}

/* Modal styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
}

.modal-content {
    background-color: white;
    margin: 2rem auto;
    padding: 0;
    border: 1px solid #d1d5db;
    max-width: 64rem;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1rem 1.5rem;
    background-color: #1e293b;
    color: white;
    border-bottom: 1px solid #4b5563;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.modal-close-btn {
    color: #9ca3af;
    font-size: 1.875rem;
    font-weight: bold;
    background: transparent;
    border: none;
    cursor: pointer;
}

.modal-close-btn:hover {
    color: white;
}

.modal-body {
    padding: 0;
    flex: 1;
    overflow: hidden;
    min-height: 24rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
    text-align: right;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

.modal-footer button {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    margin-left: 0.5rem;
}

.btn-primary {
    background-color: #2563eb;
    color: white;
    border: 1px solid #2563eb;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-secondary {
    background-color: #e5e7eb;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background-color: #d1d5db;
}

/* Modal split layout */
.modal-split-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 0;
    height: 24rem;
}

.modal-left-panel {
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    background-color: #f9fafb;
}

.modal-left-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.modal-left-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.modal-left-footer {
    padding: 0.75rem;
    border-top: 1px solid #e5e7eb;
    background-color: white;
    flex-shrink: 0;
}

.modal-right-panel {
    background-color: white;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.modal-right-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
    flex-shrink: 0;
}

.modal-right-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.25rem;
}

/* Environment and template list items */
.env-list-item, .tpl-list-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
    background-color: white;
}

.env-list-item:hover, .tpl-list-item:hover {
    background-color: #eff6ff;
}

.env-list-item.active, .tpl-list-item.active {
    background-color: #dbeafe;
    border-left: 4px solid #2563eb;
    font-weight: 500;
}

.item-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s;
}

.env-list-item:hover .item-actions, .tpl-list-item:hover .item-actions {
    opacity: 1;
}

.item-actions button {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    color: #4b5563;
}

.item-actions button:hover {
    background-color: #e5e7eb;
    color: #374151;
}

/* Button styles */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-sm.btn-primary {
    background-color: #2563eb;
    color: white;
}

.btn-sm.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-sm.btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-sm.btn-secondary:hover {
    background-color: #e5e7eb;
}

/* Queue item styles */
.queue-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    background-color: #f9fafb;
}

.queue-item-header {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.queue-item-header button {
    background: transparent;
    border: none;
    color: #dc2626;
    cursor: pointer;
}

.queue-item-header button:hover {
    color: #991b1b;
}

/* History item styles */
.history-item {
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    background-color: white;
}

.history-item.success {
    border-left: 4px solid #10b981;
}

.history-item.error {
    border-left: 4px solid #ef4444;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-item-details {
    font-size: 0.875rem;
    color: #4b5563;
}

/* Status code styles */
.status-code {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.status-code.success {
    color: #047857;
    background-color: #d1fae5;
}

.status-code.error {
    color: #b91c1c;
    background-color: #fee2e2;
}

.status-code.warning {
    color: #b45309;
    background-color: #fef3c7;
}

.status-code.info {
    color: #1d4ed8;
    background-color: #dbeafe;
}

/* Log message styles */
.log-info {
    color: #60a5fa;
    display: block;
    margin-bottom: 0.25rem;
}

.log-warning {
    color: #fbbf24;
    display: block;
    margin-bottom: 0.25rem;
}

.log-error {
    color: #f87171;
    display: block;
    margin-bottom: 0.25rem;
}

.log-success {
    color: #34d399;
    display: block;
    margin-bottom: 0.25rem;
}

/* Code editor toolbar */
.code-editor-container {
    position: relative;
}

.code-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
    border-bottom: none;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    font-size: 0.875rem;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.theme-selector {
    padding: 0.25rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    background-color: white;
}

/* Responsive design */
@media (max-width: 768px) {
    .modal-split-layout {
        grid-template-columns: 1fr;
    }

    .modal-left-panel {
        display: none;
    }
}

/* Additional utility classes for compatibility */
.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

.row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.row .col-md-6 {
    flex: 1;
}

/* CodeMirror theme adjustments */
.cm-s-material .CodeMirror {
    background-color: #263238;
    color: #eeffff;
}

.cm-s-material .CodeMirror-gutters {
    background-color: #37474f;
    border-right: 1px solid #37474f;
}

.cm-s-material .CodeMirror-linenumber {
    color: #546e7a;
}

.code-editor-container .CodeMirror {
    border-radius: 0 0 0.375rem 0.375rem;
    border-top: none;
}

/* Preview modal specific styles */
#previewModal .modal-content {
    max-width: 600px;
}

#previewModal .modal-header h3 {
    margin: 0;
    font-size: 1.3em;
    color: #ecf0f1;
    display: flex;
    align-items: center;
}

#previewModal .modal-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

#previewContent {
    white-space: pre-wrap;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Fira Mono', 'Droid Sans Mono', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
    padding: 20px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    background: #f8f9fa;
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

#previewContent.success {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-color: #27ae60;
    color: #2c3e50;
}

#previewContent.error {
    background: linear-gradient(135deg, #fdf2f2 0%, #fef5f5 100%);
    border-color: #e74c3c;
    color: #c0392b;
}